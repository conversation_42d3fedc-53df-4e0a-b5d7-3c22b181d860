import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# Create figure and axis
fig, ax = plt.subplots(1, 1, figsize=(12, 9))
ax.set_xlim(0, 12)
ax.set_ylim(0, 9)
ax.set_aspect('equal')

# Remove axes
ax.axis('off')

# Title
ax.text(6, 8.5, 'Block Diagram', fontsize=20, fontweight='bold', ha='center')

# Define colors for different component types
colors = {
    'control': '#FFD700',      # Gold for control blocks
    'safety': '#FF6B6B',       # Red for safety
    'comm': '#4ECDC4',         # Teal for communication
    'lsd': '#95E1D3',          # Light green for LSD
    'led': '#A8E6CF',          # Lighter green for LED
    'predriver': '#FFB3BA',    # Light pink for pre-drivers
    'mosfet': '#FFDFBA',       # Light orange for MOSFET
    'dual': '#BAE1FF',         # Light blue for dual drivers
    'central': '#F0F0F0'       # Light gray for central control
}

# Function to create rounded rectangle
def create_block(x, y, width, height, text, subtext, color, ax):
    # Create rounded rectangle
    rect = FancyBboxPatch((x, y), width, height, 
                         boxstyle="round,pad=0.05", 
                         facecolor=color, 
                         edgecolor='black', 
                         linewidth=1.5)
    ax.add_patch(rect)
    
    # Add main text
    ax.text(x + width/2, y + height/2 + 0.1, text, 
            fontsize=10, fontweight='bold', ha='center', va='center')
    
    # Add subtext
    ax.text(x + width/2, y + height/2 - 0.2, subtext, 
            fontsize=8, ha='center', va='center')

# Create blocks - Top row
create_block(0.5, 6.5, 2, 1, 'Wake Up', 'Control', colors['control'], ax)
create_block(3, 6.5, 2, 1, 'Q&A Watchdog', 'Safety Monitor', colors['safety'], ax)
create_block(5.5, 6.5, 2, 1, 'MSC Micro\nSecond Channel', 'Communication', colors['comm'], ax)
create_block(8, 6.5, 2, 1, '6x Ignition\nPre Driver', 'Ignition Control', colors['predriver'], ax)

# Second row
create_block(0.5, 5, 2, 1, '5x 0.6A LSD', 'Low Side Drivers', colors['lsd'], ax)
create_block(3, 5, 2, 1, '6x 2.2A LSD', 'Low Side Drivers', colors['lsd'], ax)
create_block(5.5, 5, 2, 1, '2x 70mA LSD\n(LED)', 'LED Drivers', colors['led'], ax)
create_block(8, 5, 2, 1, '5x MOSFET\nPre Driver', 'Power Control', colors['mosfet'], ax)

# Third row
create_block(0.5, 3.5, 2, 1, '3x 0.6A\nHSD/LSD', 'Dual Side Drivers', colors['dual'], ax)

# Central Control Unit
create_block(4, 1.5, 3, 1.5, 'Central Control\nProcessing Unit', 'System Coordination', colors['central'], ax)

# Connection lines
# From top row to central control
connections = [
    (1.5, 6.5, 5, 3),      # Wake Up
    (4, 6.5, 5.2, 3),      # Q&A Watchdog
    (6.5, 6.5, 5.8, 3),    # MSC
    (9, 6.5, 6.2, 3),      # Ignition Pre Driver
]

# From second row to central control
connections.extend([
    (1.5, 5, 4.8, 3),      # 5x 0.6A LSD
    (4, 5, 5.2, 3),        # 6x 2.2A LSD
    (6.5, 5, 5.8, 3),      # 2x 70mA LSD
    (9, 5, 6.2, 3),        # 5x MOSFET Pre Driver
])

# From third row to central control
connections.append((1.5, 3.5, 4.8, 2.5))  # 3x 0.6A HSD/LSD

# Draw connection lines
for x1, y1, x2, y2 in connections:
    ax.plot([x1, x2], [y1, y2], 'k-', linewidth=1.5, alpha=0.7)

# Add input/output labels
# Left side inputs
ax.text(0.2, 7, 'WAKE_IN', fontsize=8, rotation=90, va='center')
ax.text(0.2, 5.5, 'LOAD_CTRL', fontsize=8, rotation=90, va='center')
ax.text(0.2, 4, 'PWR_CTRL', fontsize=8, rotation=90, va='center')

# Right side outputs
ax.text(10.5, 7, 'IGN_OUT', fontsize=8, rotation=90, va='center')
ax.text(10.5, 5.5, 'PWR_OUT', fontsize=8, rotation=90, va='center')

# Bottom output
ax.text(5.5, 0.8, 'STATUS_OUT', fontsize=8, ha='center')

# Power supply information
ax.text(6, 0.3, 'Power Supply: 12V/5V/3.3V', fontsize=12, fontweight='bold', ha='center')

# Add border around entire diagram
border = patches.Rectangle((0.2, 0.1), 11.6, 8.6, linewidth=2, edgecolor='black', facecolor='none')
ax.add_patch(border)

# Save as PNG
plt.tight_layout()
plt.savefig('block_diagram.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

print("Block diagram saved as 'block_diagram.png'")
