import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle
import numpy as np

# Create figure and axis - matching original size and proportions
fig, ax = plt.subplots(1, 1, figsize=(14, 10))
ax.set_xlim(0, 14)
ax.set_ylim(0, 10)
ax.set_aspect('equal')

# Remove axes
ax.axis('off')

# Title - matching original style
ax.text(7, 9.5, '1.1    Block diagram', fontsize=14, fontweight='bold', ha='center')
ax.text(7, 9.1, 'Figure 1. Block diagram', fontsize=12, ha='center')

# Main container - gray background like original
main_container = Rectangle((1, 1), 12, 7.5, facecolor='#D3D3D3', edgecolor='black', linewidth=2)
ax.add_patch(main_container)

# Define colors matching original diagram
colors = {
    'wake': '#FFFF99',         # Yellow for Wake Up
    'watchdog': '#FF9999',     # Light red for Watchdog
    'msc': '#99CCFF',          # Light blue for MSC
    'lsd_small': '#99FF99',    # Light green for small LSD
    'lsd_large': '#99FF99',    # Light green for large LSD
    'led': '#CCFFCC',          # Very light green for LED
    'ignition': '#FFCCCC',     # Light pink for ignition
    'mosfet': '#FFFFCC',       # Light yellow for MOSFET
    'dual': '#CCCCFF'          # Light blue for dual drivers
}

# Function to create blocks matching original style
def create_block(x, y, width, height, text, color, ax, text_size=9):
    rect = Rectangle((x, y), width, height,
                    facecolor=color,
                    edgecolor='black',
                    linewidth=1)
    ax.add_patch(rect)

    # Add text
    ax.text(x + width/2, y + height/2, text,
            fontsize=text_size, fontweight='bold', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.8))

# Create the requested blocks in positions similar to original

# Wake Up - left side
create_block(2, 6.5, 1.8, 0.8, 'Wake Up', colors['wake'], ax)

# Q&A Watchdog - left side
create_block(2, 5.5, 1.8, 0.8, 'Q&A\nWatchdog', colors['watchdog'], ax)

# MSC - left side
create_block(2, 4.5, 1.8, 0.8, 'MSC\nMicro Second\nChannel', colors['msc'], ax, 8)

# LSD blocks - center area
create_block(5, 7, 1.8, 0.8, '5x 0.6A\nLSD', colors['lsd_small'], ax)
create_block(7.2, 7, 1.8, 0.8, '6x 2.2A\nLSD', colors['lsd_large'], ax)
create_block(9.4, 7, 1.8, 0.8, '2x 70mA\nLSD (LED)', colors['led'], ax, 8)

# Pre-driver blocks - right side
create_block(5, 5.5, 1.8, 0.8, '6x Ignition\nPre Driver', colors['ignition'], ax, 8)
create_block(7.2, 5.5, 1.8, 0.8, '5x MOSFET\nPre Driver', colors['mosfet'], ax, 8)

# Dual driver - bottom left
create_block(5, 4, 1.8, 0.8, '3x 0.6A\nHSD/LSD', colors['dual'], ax, 8)

# Add input pins on the left side (matching original layout)
input_pins = [
    ('VBAT_Sense', 1.5, 8.2),
    ('BOOST_LS', 1.5, 7.9),
    ('VB_IN', 1.5, 7.6),
    ('VB_IN_SW', 1.5, 7.3),
    ('VSENSE1', 1.5, 6.8),
    ('VSENSE2', 1.5, 6.5),
    ('VSENSE3', 1.5, 6.2),
    ('VSENSE4_MON', 1.5, 5.9),
    ('AD_TEST', 1.5, 5.6),
    ('KEY_IN', 1.5, 5.3),
    ('WK_IN', 1.5, 5.0),
    ('VB_STBY', 1.5, 4.7),
    ('SEQ_OUT', 1.5, 4.4),
    ('WDA', 1.5, 4.1),
    ('FLM_IN_P', 1.5, 3.8),
    ('FLM_IN_N', 1.5, 3.5),
    ('FLM_OUT', 1.5, 3.2),
    ('SCTC', 1.5, 2.9),
    ('MSC_CLK_O', 1.5, 2.6),
    ('MSC_DI_O', 1.5, 2.3),
    ('MSC_DI_EN', 1.5, 2.0),
    ('MSC_EN_MSC_DO', 1.5, 1.7)
]

# Add output pins on the right side
output_pins = [
    ('VDD5_Gate', 12.5, 8.2),
    ('VDD5_IN', 12.5, 7.9),
    ('RSTN', 12.5, 7.6),
    ('RXTL5', 12.5, 7.3),
    ('COLL&A,D2,Iout1', 12.5, 7.0),
    ('CURR_Sense_COLL2', 12.5, 6.7),
    ('CURR_Sense_COLL3', 12.5, 6.4),
    ('EN1_P', 12.5, 6.1),
    ('EN1_N', 12.5, 5.8),
    ('RU_ENA', 12.5, 5.5),
    ('SOLID_PGND', 12.5, 5.2),
    ('RU_PGND', 12.5, 4.9),
    ('LED_I2', 12.5, 4.6),
    ('IGN1,2,3,4,5,6', 12.5, 4.3),
    ('PGND1,2,3,4,5', 12.5, 4.0),
    ('PGND1,2,3,4,5_Gate', 12.5, 3.7),
    ('POR_GND', 12.5, 3.4),
    ('MRD', 12.5, 3.1),
    ('EN_P', 12.5, 2.8),
    ('EN_N', 12.5, 2.5),
    ('STR1,2,3,SRC', 12.5, 2.2)
]

# Draw input pins and labels
for label, x, y in input_pins:
    ax.plot([x-0.3, x], [y, y], 'k-', linewidth=1)
    ax.text(x-0.35, y, label, fontsize=7, ha='right', va='center')

# Draw output pins and labels
for label, x, y in output_pins:
    ax.plot([x, x+0.3], [y, y], 'k-', linewidth=1)
    ax.text(x+0.35, y, label, fontsize=7, ha='left', va='center')

# Add connection lines between blocks (simplified version of original)
# Connect inputs to blocks - Wake Up gets KEY_IN, WK_IN, VB_STBY
ax.plot([1.8, 2], [5.3, 6.9], 'k-', linewidth=1)  # KEY_IN to Wake Up
ax.plot([1.8, 2], [5.0, 6.9], 'k-', linewidth=1)  # WK_IN to Wake Up
ax.plot([1.8, 2], [4.7, 6.9], 'k-', linewidth=1)  # VB_STBY to Wake Up
ax.plot([1.8, 2], [4.1, 5.9], 'k-', linewidth=1)  # WDA to Watchdog
ax.plot([1.8, 2], [2.6, 4.9], 'k-', linewidth=1)  # MSC_CLK_O to MSC block
ax.plot([1.8, 2], [2.3, 4.9], 'k-', linewidth=1)  # MSC_DI_O to MSC block
ax.plot([1.8, 2], [2.0, 4.9], 'k-', linewidth=1)  # MSC_DI_EN to MSC block
ax.plot([1.8, 2], [1.7, 4.9], 'k-', linewidth=1)  # MSC_EN_MSC_DO to MSC block

# Connect blocks to outputs
ax.plot([9.4, 11.5], [7.4, 4.6], 'k-', linewidth=1)  # LED block to LED output
ax.plot([7.2, 11.5], [5.9, 4.3], 'k-', linewidth=1)  # Ignition to IGN output
ax.plot([7.2, 11.5], [5.9, 4.0], 'k-', linewidth=1)  # MOSFET to PGND output

# Internal connections between blocks
ax.plot([3.8, 5], [6.9, 7.4], 'k-', linewidth=1)    # Wake Up to LSD
ax.plot([3.8, 5], [5.9, 5.9], 'k-', linewidth=1)    # Watchdog to Ignition
ax.plot([3.8, 5], [4.9, 4.4], 'k-', linewidth=1)    # MSC to HSD/LSD

# Add bottom labels
ax.text(7, 0.5, 'SPC574K72XXXXX', fontsize=10, ha='center', style='italic')

# Save as PNG
plt.tight_layout()
plt.savefig('block_diagram.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

print("Block diagram saved as 'block_diagram.png'")
